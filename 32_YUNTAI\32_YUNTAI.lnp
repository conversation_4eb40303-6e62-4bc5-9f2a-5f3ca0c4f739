--cpu Cortex-M3
"32_yuntai\startup_stm32f103xb.o"
"32_yuntai\main.o"
"32_yuntai\gpio.o"
"32_yuntai\dma.o"
"32_yuntai\tim.o"
"32_yuntai\usart.o"
"32_yuntai\stm32f1xx_it.o"
"32_yuntai\stm32f1xx_hal_msp.o"
"32_yuntai\stm32f1xx_hal_gpio_ex.o"
"32_yuntai\stm32f1xx_hal_tim.o"
"32_yuntai\stm32f1xx_hal_tim_ex.o"
"32_yuntai\stm32f1xx_hal.o"
"32_yuntai\stm32f1xx_hal_rcc.o"
"32_yuntai\stm32f1xx_hal_rcc_ex.o"
"32_yuntai\stm32f1xx_hal_gpio.o"
"32_yuntai\stm32f1xx_hal_dma.o"
"32_yuntai\stm32f1xx_hal_cortex.o"
"32_yuntai\stm32f1xx_hal_pwr.o"
"32_yuntai\stm32f1xx_hal_flash.o"
"32_yuntai\stm32f1xx_hal_flash_ex.o"
"32_yuntai\stm32f1xx_hal_exti.o"
"32_yuntai\stm32f1xx_hal_uart.o"
"32_yuntai\system_stm32f1xx.o"
"32_yuntai\uart_app.o"
"32_yuntai\color.o"
"32_yuntai\yuntai.o"
"32_yuntai\initial.o"
"32_yuntai\wit_c_sdk.o"
"32_yuntai\emm_v5.o"
"32_yuntai\motor_function.o"
--library_type=microlib --strict --scatter "32_YUNTAI\32_YUNTAI.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "32_YUNTAI.map" -o 32_YUNTAI\32_YUNTAI.axf